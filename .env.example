# Application Configuration
APP_NAME=FastAPI Template
APP_VERSION=0.1.0
DEBUG=true

# Server Configuration
HOST=0.0.0.0
PORT=8000
RELOAD=true

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:8000"]

# API Configuration
API_V1_PREFIX=/api/v1

# Database Configuration (uncomment and modify as needed)
# DATABASE_URL=**************************************************/fastapi_db
# DATABASE_URL=sqlite:///./app.db

# Redis Configuration (uncomment if using Redis)
# REDIS_URL=redis://redis:6379/0

# Security (generate secure values for production)
# SECRET_KEY=your-secret-key-here
# ACCESS_TOKEN_EXPIRE_MINUTES=30

# Logging
LOG_LEVEL=debug
