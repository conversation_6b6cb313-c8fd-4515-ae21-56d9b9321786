.PHONY: help install install-dev test lint format clean run run-dev run-prod docker-build docker-run docker-dev docker-prod

help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## Install production dependencies
	uv sync --no-dev

install-dev: ## Install development dependencies
	uv sync --dev
	uv run pre-commit install

test: ## Run tests
	uv run pytest

test-cov: ## Run tests with coverage
	uv run pytest --cov=app --cov-report=html --cov-report=term

lint: ## Run linting
	uv run ruff check .
	uv run mypy .

format: ## Format code
	uv run ruff format .
	uv run ruff check --fix .

clean: ## Clean up cache and temporary files
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf dist
	rm -rf build
	rm -rf *.egg-info

run: ## Run the application
	uv run python -m app.main

run-dev: ## Run the application in development mode
	uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

run-prod: ## Run the application in production mode
	uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4

# Docker commands
docker-build: ## Build development Docker image
	docker build -f Dockerfile.dev -t fastapi-template:dev .

docker-build-prod: ## Build production Docker image
	docker build -f Dockerfile.prod -t fastapi-template:prod .

docker-run: ## Run development container
	docker run -p 8000:8000 -v $(PWD):/app fastapi-template:dev

docker-dev: ## Start development environment with Docker Compose
	docker-compose up --build

docker-prod: ## Start production environment with Docker Compose
	docker-compose -f docker-compose.prod.yml up --build

docker-down: ## Stop Docker Compose services
	docker-compose down

docker-down-prod: ## Stop production Docker Compose services
	docker-compose -f docker-compose.prod.yml down

# Environment setup
setup-env: ## Copy environment file template
	cp .env.example .env
	@echo "Please edit .env file with your configuration"

setup-env-prod: ## Copy production environment file template
	cp .env.prod.example .env.prod
	@echo "Please edit .env.prod file with your production configuration"
