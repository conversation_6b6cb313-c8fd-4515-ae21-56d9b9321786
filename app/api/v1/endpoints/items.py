"""Item endpoints."""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()


class Item(BaseModel):
    """Item model."""
    id: int
    name: str
    description: str | None = None
    price: float


class ItemCreate(BaseModel):
    """Item creation model."""
    name: str
    description: str | None = None
    price: float


# Mock data for demonstration
items_db = [
    Item(id=1, name="Laptop", description="High-performance laptop", price=999.99),
    Item(id=2, name="Mouse", description="Wireless mouse", price=29.99),
]


@router.get("/", response_model=list[Item])
async def get_items():
    """Get all items."""
    return items_db


@router.get("/{item_id}", response_model=Item)
async def get_item(item_id: int):
    """Get an item by ID."""
    for item in items_db:
        if item.id == item_id:
            return item
    raise HTTPException(status_code=404, detail="Item not found")


@router.post("/", response_model=Item)
async def create_item(item: ItemCreate):
    """Create a new item."""
    new_id = max(i.id for i in items_db) + 1 if items_db else 1
    new_item = Item(id=new_id, name=item.name, description=item.description, price=item.price)
    items_db.append(new_item)
    return new_item


@router.put("/{item_id}", response_model=Item)
async def update_item(item_id: int, item: ItemCreate):
    """Update an item by ID."""
    for i, existing_item in enumerate(items_db):
        if existing_item.id == item_id:
            updated_item = Item(id=item_id, name=item.name, description=item.description, price=item.price)
            items_db[i] = updated_item
            return updated_item
    raise HTTPException(status_code=404, detail="Item not found")


@router.delete("/{item_id}")
async def delete_item(item_id: int):
    """Delete an item by ID."""
    for i, item in enumerate(items_db):
        if item.id == item_id:
            del items_db[i]
            return {"message": "Item deleted successfully"}
    raise HTTPException(status_code=404, detail="Item not found")
