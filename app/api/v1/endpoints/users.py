"""User endpoints."""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()


class User(BaseModel):
    """User model."""
    id: int
    name: str
    email: str


class UserCreate(BaseModel):
    """User creation model."""
    name: str
    email: str


# Mock data for demonstration
users_db = [
    User(id=1, name="<PERSON>", email="<EMAIL>"),
    User(id=2, name="<PERSON>", email="<EMAIL>"),
]


@router.get("/", response_model=list[User])
async def get_users():
    """Get all users."""
    return users_db


@router.get("/{user_id}", response_model=User)
async def get_user(user_id: int):
    """Get a user by ID."""
    for user in users_db:
        if user.id == user_id:
            return user
    raise HTTPException(status_code=404, detail="User not found")


@router.post("/", response_model=User)
async def create_user(user: UserCreate):
    """Create a new user."""
    new_id = max(u.id for u in users_db) + 1 if users_db else 1
    new_user = User(id=new_id, name=user.name, email=user.email)
    users_db.append(new_user)
    return new_user


@router.delete("/{user_id}")
async def delete_user(user_id: int):
    """Delete a user by ID."""
    for i, user in enumerate(users_db):
        if user.id == user_id:
            del users_db[i]
            return {"message": "User deleted successfully"}
    raise HTTPException(status_code=404, detail="User not found")
