version: '3.8'

services:
  # FastAPI application (development)
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/.venv  # Exclude virtual environment from volume mount
    environment:
      - DEBUG=true
      - RELOAD=true
      - LOG_LEVEL=debug
    env_file:
      - .env
    depends_on:
      - db
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # PostgreSQL database (optional - uncomment if needed)
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fastapi_db
      POSTGRES_USER: fastapi_user
      POSTGRES_PASSWORD: fastapi_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped

  # Redis cache (optional - uncomment if needed)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx reverse proxy (optional - for production-like setup)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - app
    networks:
      - app-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
